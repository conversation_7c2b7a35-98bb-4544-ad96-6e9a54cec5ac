<script lang="ts">
	import { t } from '$lib/stores/i18n';
	import {
		PlusOutline,
		MinusOutline,
		TrashBinSolid,
		EditOutline,
		CheckOutline
	} from 'flowbite-svelte-icons';
	import { enhance } from '$app/forms';
	import { invalidateAll } from '$app/navigation';
	import { Accordion, AccordionItem, Indicator, Button } from 'flowbite-svelte';
	import { onMount, tick } from 'svelte';

	// Tags variables & functions
	export let partnerNames = [];
	import { colorOptions, getColorClass } from '$lib/utils'; // adjust the path if needed

	let isAddingPartner = false;
	let formErrors: string | null = null;
	let partnerToDelete: number | null = null;
	let partnerToEdit: number | null = null;
	let isSubmitting = false;

	// Color picker state
	let colorPickerOpen = false;
	let activePickerId: string | null = null;

	// State for editing
	let selectedColor = colorOptions[0]?.name || 'gray';

	// State for new partner
	let newPartnerColor = colorOptions[0]?.name || 'gray';

	onMount(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (colorPickerOpen && !(event.target as HTMLElement).closest('.color-picker-area')) {
				colorPickerOpen = false;
				activePickerId = null;
			}
		};
		document.addEventListener('click', handleClickOutside);
		return () => document.removeEventListener('click', handleClickOutside);
	});

	async function toggleColorPicker(id: string, event: MouseEvent) {
		event.preventDefault();
		event.stopPropagation();

		if (activePickerId === id && colorPickerOpen) {
			colorPickerOpen = false;
			activePickerId = null;
		} else {
			activePickerId = id;
			await tick();
			colorPickerOpen = true;
		}
	}

	function chooseColor(name: string, event: MouseEvent) {
		event.preventDefault();
		event.stopPropagation();

		if (partnerToEdit !== null) {
			selectedColor = name;
		} else {
			newPartnerColor = name;
		}
		colorPickerOpen = false;
		activePickerId = null;
	}

	function handleSubmit() {
		isSubmitting = true;
		formErrors = null;

		return async ({ result, update }) => {
			isSubmitting = false;

			if (result.type === 'success') {
				// Reset form and hide the form
				const form = document.querySelector('form') as HTMLFormElement;
				form?.reset();
				isAddingPartner = false;
				partnerToEdit = null;

				// Invalidate all data to force a full reload
				await invalidateAll();
			} else if (result.type === 'failure') {
				formErrors = result.data?.error || 'An unexpected error occurred';
			}
		};
	}

	function confirmDelete(partnerId: number) {
		partnerToDelete = partnerId;
	}

	function cancelDelete() {
		partnerToDelete = null;
	}

	function startEdit(partner) {
		partnerToEdit = partner.id;
		selectedColor = partner.color || 'gray';
		activePickerId = null;
		formErrors = null;
	}

	function cancelEdit() {
		partnerToEdit = null;
		formErrors = null;
		colorPickerOpen = false;
		activePickerId = null;
	}
</script>

<AccordionItem>
	<span slot="header" class="flex w-full flex-col">
		<h2 class="text-xl font-medium text-gray-700">{t('partners')}</h2>
		<p class="text-sm text-gray-500">{t('partners_description')}</p>
	</span>

	<div class="space-y-3">
		{#if partnerNames.length > 0}
			{#each partnerNames as partner (partner.id)}
				<li class="flex items-center justify-between rounded-lg px-4 py-2">
					{#if partnerToEdit === partner.id}
						<div class="w-full relative flex items-center gap-3">
							<button
								type="button"
								class="flex items-center"
								on:click={(event) => toggleColorPicker(`partner-${partner.id}`, event)}
								aria-label="Select color"
							>
								<Indicator size="lg" class={`mr-1 ${getColorClass(selectedColor)}`} />
							</button>
							{#if activePickerId === `partner-${partner.id}` && colorPickerOpen}
								<div
									class="color-picker-area absolute bottom-full left-0 z-20 mb-2 rounded-lg bg-white p-3 shadow-lg"
									style="min-width: 170px;"
								>
									<div class="grid grid-cols-6 gap-3">
										{#each colorOptions as opt}
											<button
												type="button"
												class={`h-6 w-6 cursor-pointer rounded-full ${opt.class} border ${selectedColor === opt.name ? 'ring-2 ring-gray-400' : 'border-transparent'}`}
												on:click={(event) => chooseColor(opt.name, event)}
												aria-label={`Select ${opt.name} color`}
											></button>
										{/each}
									</div>
								</div>
							{/if}

							<form
								method="POST"
								action="?/update_partner"
								use:enhance={handleSubmit}
								class="flex flex-1 items-center gap-2"
							>
								<input type="hidden" name="backend_company_id" value={partner.id} />
								<input type="hidden" name="color" value={selectedColor} />
								<input
									type="text"
									id="name"
									name="name"
									required
									value={partner.name}
									class="flex-1 rounded-lg border border-gray-300 px-4 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
									placeholder={t('enter_partner_name')}
								/>

								<input
									type="text"
									id="code"
									name="code"
									required
									value={partner.code}
									class="flex-1 rounded-lg border border-gray-300 px-4 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
									placeholder={t('enter_partner_code')}
								/>

								<Button type="submit" disabled={isSubmitting} color="green">
									<CheckOutline class="mr-2 h-4 w-4" />
									{isSubmitting ? t('updating') : t('save')}
								</Button>
								<Button type="button" on:click={cancelEdit} color="light">
									{t('cancel')}
								</Button>
							</form>
						</div>
						{#if formErrors}
							<div class="mt-2 rounded-lg bg-red-100 p-3 text-red-700">{formErrors}</div>
						{/if}
					{:else}
						<div class="flex items-center">
							<!-- <Indicator size="lg" color={partner.color ?? 'gray'} class="mr-2" />  -->
							<Indicator size="lg" class={`${getColorClass(partner.color)} mr-2`} />
							<div>
								<span class="font-xl text-md text-gray-900">{partner.name}</span>
								<span class="text-sm text-gray-500">({partner.code})</span>
							</div>
						</div>

						<div class="flex items-center space-x-2">
							{#if partnerToDelete === partner.id}
								<div class="flex space-x-2">
									<form
										method="POST"
										action="?/delete_partner"
										use:enhance={handleSubmit}
										class="flex items-center"
									>
										<input type="hidden" name="backend_company_id" value={partner.id} />
										<Button type="submit" disabled={isSubmitting} color="red">
											<TrashBinSolid class="mr-2 h-4 w-4" />
											{t('delete')}
										</Button>
									</form>
									<Button on:click={cancelDelete} color="light">
										{t('cancel')}
									</Button>
								</div>
							{:else}
								<button
									on:click={() => startEdit(partner)}
									class="text-gray-400 hover:text-gray-800"
								>
									<EditOutline class="h-5 w-5" />
								</button>
								<button
									on:click={() => confirmDelete(partner.id)}
									class="text-red-500 hover:text-red-700"
								>
									<TrashBinSolid class="h-5 w-5" />
								</button>
							{/if}
						</div>
					{/if}
				</li>
				<!-- Divider line between sections -->
				<hr class="my-6 border-t border-gray-300" />
			{/each}
		{:else if !isAddingPartner}
			<p class="text-center italic text-gray-500">{t('no_partners')}</p>
		{/if}

		<!-- New Partner Form -->
		<div class="w-full">
			{#if isAddingPartner}
				<div class="relative flex items-center gap-3 pr-4">
					<button
						type="button"
						class="flex items-center pl-4"
						on:click={(event) => toggleColorPicker('new-partner', event)}
						aria-label="Select color"
					>
						<!-- <Indicator size="lg" color={newPartnerColor} class="mr-2" /> -->
						<Indicator size="lg" class={`mr-1 ${getColorClass(newPartnerColor)}`} />
					</button>
					{#if activePickerId === 'new-partner' && colorPickerOpen}
						<div
							class="color-picker-area absolute bottom-full left-0 z-20 mb-2 rounded-lg bg-white p-3 shadow-lg"
							style="min-width: 170px;"
						>
							<div class="grid grid-cols-6 gap-3">
								{#each colorOptions as opt}
									<button
										type="button"
										class={`h-6 w-6 cursor-pointer rounded-full ${opt.class} border ${newPartnerColor === opt.name ? 'ring-2 ring-gray-400' : 'border-transparent'}`}
										on:click={(event) => chooseColor(opt.name, event)}
										aria-label={`Select ${opt.name} color`}
									></button>
								{/each}
							</div>
						</div>
					{/if}

					<form
						method="POST"
						action="?/create_new_partner_action"
						use:enhance={handleSubmit}
						class="flex flex-1 items-center gap-2"
					>
						<input type="hidden" name="color" value={newPartnerColor} />
						<input
							type="text"
							id="name"
							name="name"
							required
							class="flex-1 rounded-lg border border-gray-300 px-4 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
							placeholder={t('partner_name_placeholder')}
						/>

						<input
							type="text"
							id="code"
							name="code"
							required
							class="flex-1 rounded-lg border border-gray-300 px-4 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
							placeholder={t('partner_abbreviation_placeholder')}
						/>

						<Button type="submit" disabled={isSubmitting} color="green" class="disabled:opacity-20">
							<CheckOutline class="mr-2 h-4 w-4" />
							{isSubmitting ? t('creating') : t('add_partner')}
						</Button>
						<Button
							type="button"
							on:click={() => {
								isAddingPartner = !isAddingPartner;
								formErrors = null;
							}}
							color="light"
						>
							{t('cancel')}
						</Button>
					</form>
				</div>
			{:else}
				<div class="flex justify-start">
					<Button
						on:click={() => {
							isAddingPartner = !isAddingPartner;
							formErrors = null;
						}}
						color="blue"
					>
						<PlusOutline class="mr-2 inline-block h-4 w-4" />
						{t('add_partner')}
					</Button>
				</div>
			{/if}

			{#if formErrors}
				<div class="rounded-lg bg-red-100 p-3 text-red-700">{formErrors}</div>
			{/if}
		</div>
	</div>
</AccordionItem>
